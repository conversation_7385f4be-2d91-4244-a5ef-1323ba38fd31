package com.trinasolar.integration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.trinasolar.integration.service.DevOpsProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DevOpsProjectServiceImpl 集成测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
class DevOpsProjectServicelTest {

    @Autowired
    private DevOpsProjectService devOpsProjectService;

    /**
     * 测试 addMavenSetting 方法 - 成功场景
     * 验证当DevOps API返回成功响应时，方法返回true
     */
    @Test
    void testAddMavenSetting_Success() {

        String projectIds = "ha34c5,z7ad06,pedf8d,z7ad06,cd53f3,t66a3c,y6c0c1,r8123a,caaa81,f1b7bd,p8c12b,c90ffb";
        for (String projectId : projectIds.split(",")) {
            boolean result = devOpsProjectService.addMavenSetting(projectId);
            System.out.println(projectId + "----" + result);
        }
    }
}
