# 测试环境配置
spring:
  profiles:
    active: test
  datasource:
    # 使用H2内存数据库进行测试
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# DevOps相关配置
trinasolar:
  devops:
    domain-host: https://test-devops.trinasolar.com
    docker-img-host: https://test-docker.trinasolar.com
    login-host: https://test-login.trinasolar.com

# 日志配置
logging:
  level:
    com.trinasolar.integration: DEBUG
    org.springframework.web: DEBUG
    org.springframework.test: DEBUG
