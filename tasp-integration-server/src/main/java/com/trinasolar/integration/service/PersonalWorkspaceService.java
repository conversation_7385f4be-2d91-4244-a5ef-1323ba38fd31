package com.trinasolar.integration.service;


import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.controller.personal.UerPersonalConfigBO;
import com.trinasolar.integration.dto.appLogs.SreLogPageDTO;
import com.trinasolar.integration.dto.appLogs.SreLogQueryDTO;
import jakarta.servlet.http.HttpServletResponse;


public interface PersonalWorkspaceService {
    User getUserInfo();

    boolean saveUserConfig(UerPersonalConfigBO uerPersonalConfigBO);

    UerPersonalConfigBO getUserConfig(String userId);
}
