package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.UserDashboardConfigDO;
import com.trinasolar.integration.controller.personal.TagConfigBO;
import com.trinasolar.integration.controller.personal.UerPersonalConfigBO;
import com.trinasolar.integration.controller.personal.UserAccessConfigBO;
import com.trinasolar.integration.dao.UserAccessConfigMapper;
import com.trinasolar.integration.dao.UserDashboardConfigMapper;
import com.trinasolar.integration.service.PersonalWorkspaceService;
import com.trinasolar.integration.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class PersonalWorkSpaceServiceImpl implements PersonalWorkspaceService {

    @Autowired
    private UserAccessConfigMapper userAccessConfigMapper;

    @Autowired
    private UserDashboardConfigMapper userDashboardConfigMapper;

    @Override
    public User getUserInfo() {
        return RequestUtils.getCurrentUser();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserConfig(UerPersonalConfigBO configBO) {
        if (Objects.nonNull(configBO) && Objects.nonNull(configBO.getUserId())) {
            User currentUser = new User();
            currentUser.setUsername("test");
            LocalDateTime now = LocalDateTime.now();
            // 快捷入口
            if (CollectionUtil.isNotEmpty(configBO.getAccessConfigs())){
                userAccessConfigMapper.deleteByUserId(configBO.getUserId());
                configBO.getAccessConfigs().forEach(access -> {
                    access.setCreator(currentUser.getUsername());
                    access.setUpdater(currentUser.getUsername());
                    access.setUpdateTime(now);
                    access.setCreateTime(now);
                    access.setAccessType(3);
                });
                userAccessConfigMapper.insertBatch(configBO.getAccessConfigs());
            }

            // 工作项设置和模块显示设置
            if (CollectionUtil.isNotEmpty(configBO.getWorkItems()) || CollectionUtil.isNotEmpty(configBO.getModuleShows())){
                userDashboardConfigMapper.deleteByUserId(configBO.getUserId());
                UserDashboardConfigDO dashboardConfig = new UserDashboardConfigDO();
                dashboardConfig.setUserId(configBO.getUserId());
                dashboardConfig.setCreator(currentUser.getUsername());
                dashboardConfig.setUpdater(currentUser.getUsername());
                dashboardConfig.setUpdateTime(now);
                dashboardConfig.setCreateTime(now);
                dashboardConfig.setWorkItem(JSON.toJSONString(configBO.getWorkItems()));
                dashboardConfig.setModuleShow(JSON.toJSONString(configBO.getModuleShows()));
                userDashboardConfigMapper.insert(dashboardConfig);
            }
            return true;
        }
        return false;
    }

    @Override
    public UerPersonalConfigBO getUserConfig(String userId) {
        UerPersonalConfigBO configBO = new UerPersonalConfigBO();
        if (Objects.nonNull(userId)) {
            configBO.setUserId(userId);
            // 快捷入口
            List<UserAccessConfigBO> accessConfigs = userAccessConfigMapper.selectByUserId(userId);
            if (CollectionUtil.isEmpty(accessConfigs)){
                accessConfigs = userAccessConfigMapper.selectDefaultConfig(2);
            }
            configBO.setAccessConfigs(accessConfigs);

            // 工作项设置和模块显示设置
            UserDashboardConfigDO dashboardConfig = userDashboardConfigMapper.selectByUserId(userId);
            if (Objects.nonNull(dashboardConfig)) {
                configBO.setWorkItems(JSON.parseArray(dashboardConfig.getWorkItem(), TagConfigBO.class));
                configBO.setModuleShows(JSON.parseArray(dashboardConfig.getModuleShow(), TagConfigBO.class));
            }else {
                // 初始化数据（产品固定为以下内容，不会变动）
                configBO.setWorkItems(builderWorkItems());
                configBO.setModuleShows(builderModule());
            }
        }
        return configBO;
    }

    private List<TagConfigBO> builderModule() {
        return new ArrayList<>(){{
            add(new TagConfigBO("个人简介", 1, true));
            add(new TagConfigBO("工作事项", 2, true));
            add(new TagConfigBO("文档收藏", 3, true));
            add(new TagConfigBO("快捷入口", 4, true));
            add(new TagConfigBO("最近访问应用系统", 5, true));
            add(new TagConfigBO("最近访问应用程序", 6, true));
        }};
    }

    private List<TagConfigBO> builderWorkItems() {
        return new ArrayList<>(){{
            add(new TagConfigBO("PaaS", 1, true));
            add(new TagConfigBO("存储", 2, true));
            add(new TagConfigBO("文档", 3, true));
            add(new TagConfigBO("API", 4, true));
            add(new TagConfigBO("需求", 5, true));
            add(new TagConfigBO("任务", 6, true));
            add(new TagConfigBO("缺陷", 7, true));
            add(new TagConfigBO("TIS工单", 8, true));
            add(new TagConfigBO("合并请求", 9, true));
        }};
    }
}
