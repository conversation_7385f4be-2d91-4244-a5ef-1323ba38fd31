package com.trinasolar.integration.controller.apimarket;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.dto.apimarket.ApiDetailInfo;
import com.trinasolar.integration.dto.apimarket.UserDTO;
import com.trinasolar.integration.service.appmarket.ApiMarketService;
import com.trinasolar.tasc.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Tag(name = "API市场", description = "API市场相关接口")
@RestController
@RequestMapping("/app-market")
public class ApiMarketController {

    @Autowired
    private ApiMarketService apiMarketService;

    @Operation(summary = "发送API请求", description = "转发API请求到目标服务，返回包含耗时、状态码、响应头和响应内容的完整结果")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "请求成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/api/send", consumes = {MediaType.APPLICATION_JSON_VALUE,
            MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    public CommonResult<Map<String, Object>> sendApi(
            @ApiParam(value = "HTTP请求方法", required = true, example = "GET")
            @RequestParam(value = "api_method") String method,
            @ApiParam(value = "目标API URL", required = true, example = "https://api.example.com/data")
            @RequestParam(value = "api_url") String url,
            @ApiParam(value = "请求头JSON字符串", required = false, example = "{\"Content-Type\":\"application/json\",\"Authorization\":\"Bearer token\"}")
            @RequestParam(value = "api_header", required = false) String apiHeaderJson,
            @ApiParam(value = "请求体内容", required = false, example = "{\"key\":\"value\"}")
            @RequestBody Object body,
            @ApiParam(value = "路径参数JSON字符串", required = false, example = "{\"id\":123}")
            @RequestParam(required = false, value = "api_pathParams") String pathParams,
            @ApiParam(value = "查询参数键值对", required = false)
            @RequestParam(required = false) Map<String, String> params,
            @ApiParam(value = "文件上传参数", required = false)
            @RequestPart(required = false) Map<String, MultipartFile> files) throws Exception {
        // 初始化DTO（处理JSON和表单两种请求格式）
        SendApiDTO sendApiDTO = new SendApiDTO();
        sendApiDTO.setMethod(method);
        sendApiDTO.setUrl(url);
        sendApiDTO.setBody(body);
        // 解析JSON格式的复杂参数
        ObjectMapper objectMapper = new ObjectMapper();
        // 解析JSON格式的请求头参数
        if (StringUtils.isNotEmpty(apiHeaderJson)) {
            sendApiDTO.setHeader(objectMapper.readValue(apiHeaderJson,
                    new TypeReference<Map<String, String>>() {
                    }));
        }
        if (StringUtils.isNotEmpty(pathParams)) {
            sendApiDTO.setPathParams(objectMapper.readValue(pathParams,
                    new TypeReference<Map<String, String>>() {
                    }));
        }
        // 直接使用合并后的参数作为查询参数,过滤特殊字段
        List<String> apiKey = List.of("api_method", "api_url", "api_header","api_pathParams");
        apiKey.forEach(params.keySet()::remove);
        sendApiDTO.setQueryParams(params);
        // 设置文件参数
        if (files != null) {
            sendApiDTO.setFiles(files);
        }
        return CommonResult.success(apiMarketService.sendApi(sendApiDTO));
    }

    @Operation(summary = "创建Token", description = "创建一个新的访问Token")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = String.class)))
    @GetMapping("/api/create")
    public CommonResult<String> checkToken() {
        return CommonResult.success(apiMarketService.createToken());
    }

    @Operation(summary = "获取API卡片列表", description = "分页查询API卡片信息")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = PageResultDTO.class)))
    @GetMapping("/api/page")
    public CommonResult<PageResultDTO> getApiCardList(
            @Parameter(description = "API名称", in = ParameterIn.QUERY) @RequestParam(required = false) String name,
            @Parameter(description = "API路径", in = ParameterIn.QUERY) @RequestParam(required = false) String apiPath,
            @Parameter(description = "发布机构名称", in = ParameterIn.QUERY) @RequestParam(required = false) String pubOrgName,
            @Parameter(description = "开始时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "startTime") String startTime,
            @Parameter(description = "结束时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "endTime") String endTime,
            @Parameter(description = "描述", in = ParameterIn.QUERY) @RequestParam(required = false) String desc,
            @Parameter(description = "每页大小", required = true, in = ParameterIn.QUERY) @RequestParam("pageSize") Integer pageSize,
            @Parameter(description = "页码", required = true, in = ParameterIn.QUERY) @RequestParam("pageNo") Integer pageNo,
            @Parameter(description = "标签ID", in = ParameterIn.QUERY) @RequestParam(required = false) Integer tagId) {
        PageResultDTO serviceResult = apiMarketService.getApiCardList(name, apiPath, pubOrgName, startTime,
                endTime, desc, pageSize, pageNo, tagId);
        return CommonResult.success(serviceResult);
    }

    @Operation(summary = "验证Token", description = "验证Token的有效性")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = UserDTO.class)))
    @ApiResponse(responseCode = "400", description = "Token无效")
    @GetMapping("/api/check")
    public CommonResult<UserDTO> checkToken(
            @Parameter(description = "访问Token", required = true, in = ParameterIn.HEADER) @RequestHeader("token") String token) {
        // 后续优化统一异常
        UserDTO userDTO = null;
        try {
            userDTO = apiMarketService.checkToken(token);
        } catch (Exception e) {
            return CommonResult.error(GlobalErrorCodeConstants.ERROR_CODE.getCode(), "token is error");
        }
        return CommonResult.success(userDTO);
    }

    @Operation(summary = "获取标签列表", description = "获取所有API标签")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = TagDTO.class)))
    @GetMapping("/api/tags")
    public CommonResult<List<TagDTO>> getTags() {
        return CommonResult.success(apiMarketService.getTags());
    }

    @Operation(summary = "获取API详情", description = "根据ID获取API详细信息")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = ApiDetailInfo.class)))
    @GetMapping("/api/detail")
    public CommonResult<ApiDetailInfo> getDetail(
            @Parameter(description = "API ID", required = true, in = ParameterIn.QUERY) @RequestParam("id") Long id) {
        return CommonResult.success(apiMarketService.getDetail(id));
    }

    @Operation(summary = "获取子机构列表", description = "获取所有子机构信息")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = OrgDTO.class)))
    @GetMapping("/api/org")
    public CommonResult<List<OrgDTO>> getSubOrg(@RequestParam("pubOrgId") Long pubOrgId) {
        return CommonResult.success(apiMarketService.getSubOrg(pubOrgId));
    }

    /**
     * 创建审批流程
     *
     * @param subscribeApiDTO
     * @return
     */
    @Operation(summary = "订阅API", description = "创建API订阅申请")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = Boolean.class)))
    @PostMapping("/api/subscribe")
    public CommonResult<Boolean> subscribeApi(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "订阅信息", required = true, content = @Content(schema = @Schema(implementation = SubscribeApiDTO.class))) @RequestBody SubscribeApiDTO subscribeApiDTO) {
        return CommonResult.success(apiMarketService.subscribeApi(subscribeApiDTO));
    }

    /**
     * 查询审批用户
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取审批人列表", description = "根据ID获取审批人列表")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = String.class)))
    @GetMapping("/approve/{id}")
    public CommonResult<List<String>> getApproveUsers(
            @Parameter(description = "审批ID", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id) {
        return CommonResult.success(apiMarketService.getApproveUsers(id));
    }
    /**
     * 查询审批用户
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取审批人列表", description = "根据发布组ID获取审批人列表")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = String.class)))
    @GetMapping("/approve/pub/{id}")
    public CommonResult<List<String>> getApproveUsersByPubOrgId(
            @Parameter(description = "审批ID", required = true, in = ParameterIn.PATH) @PathVariable("id") Long id) {
        return CommonResult.success(apiMarketService.getApproveUsersByPubOrgId(id));
    }

    /**
     * 订阅审批完成回调接口
     *
     * @param apiRollbackDTO
     * @return
     */
    @Operation(summary = "审批回调", description = "审批完成后的回调接口")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = ApiApproveDTO.class)))
    @PostMapping("/approve/rollback")
    public CommonResult<ApiApproveDTO> rollback(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "回调信息", required = true, content = @Content(schema = @Schema(implementation = ApiRollbackDTO.class))) @RequestBody ApiRollbackDTO apiRollbackDTO) {
        return CommonResult.success(apiMarketService.rollback(apiRollbackDTO));
    }

    /**
     * 发布审批完成回调接口
     *
     * @param apiRollbackDTO
     * @return
     */
    @Operation(summary = "审批回调", description = "审批完成后的回调接口")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = ApiApproveDTO.class)))
    @PostMapping("/approve/pub/rollback")
    public CommonResult<ApiApproveDTO> pubRollback(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "回调信息", required = true, content = @Content(schema = @Schema(implementation = ApiRollbackDTO.class))) @RequestBody ApiRollbackDTO apiRollbackDTO) {
        return CommonResult.success(apiMarketService.pubRollback(apiRollbackDTO));
    }

    /**
     * API我的订阅-服务列表查询
     */
    @Operation(summary = "获取订阅订单列表", description = "分页查询我的订阅订单")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = OrderPage.class)))
    @ApiResponse(responseCode = "400", description = "参数错误")
    @GetMapping("/subscribe/order-page")
    public CommonResult<OrderPage> getOrderPage(
            @Parameter(description = "产品名称", in = ParameterIn.QUERY) @RequestParam(required = false) String productName,
            @Parameter(description = "API路径", in = ParameterIn.QUERY) @RequestParam(required = false) String apiPath,
            @Parameter(description = "发布组", in = ParameterIn.QUERY) @RequestParam(required = false) String pubOrgName,
            @Parameter(description = "开始时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "startTime") String startTime,
            @Parameter(description = "结束时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "endTime") String endTime,
            @Parameter(description = "每页大小", required = true, in = ParameterIn.QUERY) @RequestParam("pageSize") Integer pageSize,
            @Parameter(description = "页码", required = true, in = ParameterIn.QUERY) @RequestParam("pageNo") Integer pageNo) {
        OrderPage orderPage = apiMarketService.getMyOrderPage(productName, apiPath, pubOrgName, startTime,
                endTime, pageSize, pageNo);
        return CommonResult.success(orderPage);
    }

    /**
     * API调用日志列表
     */
    @Operation(summary = "获取调用日志", description = "分页查询API调用日志")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = SubLogDTO.class)))
    @GetMapping("/subscribe/logs")
    public CommonResult<SubLogDTO> getSubLogs(
            @Parameter(description = "API路径", in = ParameterIn.QUERY) @RequestParam(required = false, value = "path") String path,
            @Parameter(description = "请求体", in = ParameterIn.QUERY) @RequestParam(required = false, value = "requestBody") String requestBody,
            @Parameter(description = "响应体", in = ParameterIn.QUERY) @RequestParam(required = false, value = "responseBody") String responseBody,
            @Parameter(description = "响应状态", in = ParameterIn.QUERY) @RequestParam(required = false, value = "responseStatus") String responseStatus,
            @Parameter(description = "开始时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "startTime") String startTime,
            @Parameter(description = "结束时间", in = ParameterIn.QUERY) @RequestParam(required = false, value = "endTime") String endTime,
            @Parameter(description = "每页大小", required = true, in = ParameterIn.QUERY) @RequestParam("pageSize") Integer pageSize,
            @Parameter(description = "页码", required = true, in = ParameterIn.QUERY) @RequestParam("pageNo") Integer pageNo) {
        return CommonResult.success(apiMarketService.getSubLogs(path, requestBody, responseBody,
                responseStatus, startTime, endTime, pageSize, pageNo));
    }

    /**
     * API订阅日志详情查询
     */
    @Operation(summary = "获取日志详情", description = "根据事件ID获取调用日志详情")
    @ApiResponse(responseCode = "200", description = "成功", content = @Content(schema = @Schema(implementation = LogDetailDTO.class)))
    @GetMapping("/subscribe/logs/detail")
    public CommonResult<LogDetailDTO> getSubLogDetail(
            @Parameter(description = "事件ID", required = true, in = ParameterIn.QUERY) @RequestParam("eventId") String eventId) {
        return CommonResult.success(apiMarketService.getSubLogDetail(eventId));
    }



    @Operation(summary = "API发布", description = "API发布流程")
    @PostMapping("/api/pub")
    public CommonResult<Boolean> apiPub(@RequestBody JSONObject jsonObject) {
        return CommonResult.success(apiMarketService.apiPub(jsonObject));
    }

    @Operation(summary = "前端数据下载", description = "将前端传递的数据生成文件并下载")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "下载成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/download")
    public ResponseEntity<StreamingResponseBody> downloadFromFrontend(
            @Parameter(description = "文件名", required = false, in = ParameterIn.QUERY) 
            @RequestParam(value = "fileName", required = false) String fileName,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "要下载的数据内容", required = true) 
            @RequestBody String fileContent) {
        // 如果未提供文件名，使用默认名称
        if (StringUtils.isEmpty(fileName)) {
            fileName = "download_" + System.currentTimeMillis() + ".txt";
        }

        // 创建流式响应体
        StreamingResponseBody responseBody = outputStream -> {
            try {
                // 将前端传递的数据写入输出流
                // 处理换行符，统一转换为系统默认换行符
                String processedContent = fileContent.replaceAll("\\r\\n|\\r|\\n", System.lineSeparator());
                outputStream.write(processedContent.getBytes());
                outputStream.flush();
            } catch (IOException e) {
                throw new RuntimeException("下载失败: " + e.getMessage());
            }
        };

        // 设置响应头，触发浏览器下载
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                .header(HttpHeaders.CONTENT_TYPE, "application/octet-stream")
                .body(responseBody);
    }

}
