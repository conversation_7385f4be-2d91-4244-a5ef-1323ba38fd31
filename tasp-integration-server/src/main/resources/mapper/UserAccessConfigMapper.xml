<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.UserAccessConfigMapper">
    <insert id="insertBatch">
        INSERT INTO tasp_user_access_config (user_id, access_type, logo_url, name, url, sort, creator, create_time, updater, update_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.accessType}, #{item.logoUrl}, #{item.name}, #{item.url}, #{item.sort}, #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime})
        </foreach>
    </insert>
    <delete id="deleteByUserId">
        DELETE FROM tasp_user_access_config WHERE user_id = #{userId}
    </delete>
    <select id="selectByUserId" resultType="com.trinasolar.integration.controller.personal.UserAccessConfigBO">
        SELECT * FROM tasp_user_access_config WHERE access_type = 1 AND user_id = #{userId}
    </select>
    <select id="selectDefaultConfig"
            resultType="com.trinasolar.integration.controller.personal.UserAccessConfigBO">
        SELECT * FROM tasp_user_access_config WHERE access_type = #{accessType}
    </select>
</mapper>
